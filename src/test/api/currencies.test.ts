import { expect } from "chai";
import * as sinon from "sinon";
import type { FastifyInstance } from "fastify";
import { create } from "../../skywind/fastify";
import currenciesApi from "../../skywind/api/currencies";
import * as currencyServices from "../../skywind/services/currencies";
import * as token from "../../skywind/token";

describe("Currencies API", () => {
    let app: FastifyInstance;
    let verifyTokenStub;
    let findCurrencyByCodeStub;
    let getAllCurrenciesStub;
    let createCurrencyStub;
    let updateCurrencyStub;
    let deleteCurrencyStub;

    beforeEach(async () => {
        app = create();
        app.register(currenciesApi(), { prefix: "/v1" });
        await app.ready();

        verifyTokenStub = sinon.stub(token, "verifyToken").resolves();
        findCurrencyByCodeStub = sinon.stub(currencyServices, "findCurrencyByCode");
        getAllCurrenciesStub = sinon.stub(currencyServices, "getAllCurrencies");
        createCurrencyStub = sinon.stub(currencyServices, "createCurrency");
        updateCurrencyStub = sinon.stub(currencyServices, "updateCurrency");
        deleteCurrencyStub = sinon.stub(currencyServices, "deleteCurrency");
    });

    afterEach(() => {
        sinon.restore();
    });

    describe("GET /v1/currencies", () => {
        it("should return all currencies", async () => {
            const mockCurrencies = [
                {
                    code: "USD",
                    name: "US Dollar",
                    iso: {
                        code: "USD",
                        number: "840",
                        minorUnits: 2
                    },
                    isVirtual: false,
                    isSocial: false,
                    disableGGR: false
                },
                {
                    code: "EUR",
                    name: "Euro",
                    iso: {
                        code: "EUR",
                        number: "978",
                        minorUnits: 2
                    },
                    isVirtual: false,
                    isSocial: false,
                    disableGGR: false
                }
            ];

            getAllCurrenciesStub.resolves(mockCurrencies);

            const response = await app.inject({
                method: "GET",
                url: "/v1/currencies?token=valid-token"
            });

            expect(response.statusCode).to.equal(200);
            expect(JSON.parse(response.payload)).to.deep.equal(mockCurrencies);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(getAllCurrenciesStub.calledOnce).to.be.true;
        });
    });

    describe("GET /v1/currencies/:code", () => {
        it("should return a currency when found", async () => {
            const mockCurrency = {
                code: "USD",
                name: "US Dollar",
                iso: {
                    code: "USD",
                    number: "840",
                    minorUnits: 2
                },
                isVirtual: false,
                isSocial: false,
                disableGGR: false
            };

            findCurrencyByCodeStub.resolves(mockCurrency);

            const response = await app.inject({
                method: "GET",
                url: "/v1/currencies/USD?token=valid-token"
            });

            expect(response.statusCode).to.equal(200);
            expect(JSON.parse(response.payload)).to.deep.equal(mockCurrency);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("USD")).to.be.true;
        });

        it("should return 404 when currency not found", async () => {
            findCurrencyByCodeStub.resolves(null);

            const response = await app.inject({
                method: "GET",
                url: "/v1/currencies/XYZ?token=valid-token"
            });

            expect(response.statusCode).to.equal(404);
            expect(JSON.parse(response.payload)).to.deep.equal({ error: "Currency with code XYZ not found" });
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("XYZ")).to.be.true;
        });
    });

    describe("POST /v1/currencies", () => {
        it("should create a virtual currency", async () => {
            const currencyData = {
                code: "BTC",
                name: "Bitcoin",
                iso: {
                    code: "BTC",
                    number: "999",
                    minorUnits: 8
                },
                isVirtual: true,
                isSocial: false,
                disableGGR: false
            };

            findCurrencyByCodeStub.resolves(null);
            createCurrencyStub.resolves(currencyData);

            const response = await app.inject({
                method: "POST",
                url: "/v1/currencies?token=valid-token",
                payload: currencyData
            });

            expect(response.statusCode).to.equal(201);
            expect(JSON.parse(response.payload)).to.deep.equal(currencyData);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("BTC")).to.be.true;
            expect(createCurrencyStub.calledOnceWith(currencyData)).to.be.true;
        });

        it("should create a social currency with origin currency", async () => {
            const currencyData = {
                code: "GOLD",
                name: "Gold Coins",
                iso: {
                    code: "GOLD",
                    number: "998",
                    minorUnits: 0
                },
                isVirtual: false,
                isSocial: true,
                disableGGR: true,
                originCurrency: {
                    currency: "USD",
                    multiplier: 0.01
                }
            };

            findCurrencyByCodeStub.resolves(null);
            createCurrencyStub.resolves(currencyData);

            const response = await app.inject({
                method: "POST",
                url: "/v1/currencies?token=valid-token",
                payload: currencyData
            });

            expect(response.statusCode).to.equal(201);
            expect(JSON.parse(response.payload)).to.deep.equal(currencyData);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("GOLD")).to.be.true;
            expect(createCurrencyStub.calledOnceWith(currencyData)).to.be.true;
        });

        it("should return error when currency already exists", async () => {
            const currencyData = {
                code: "USD",
                name: "US Dollar",
                iso: {
                    code: "USD",
                    number: "840",
                    minorUnits: 2
                }
            };

            const existingCurrency = {
                code: "USD",
                name: "US Dollar",
                iso: {
                    code: "USD",
                    number: "840",
                    minorUnits: 2
                },
                isVirtual: false,
                isSocial: false,
                disableGGR: false
            };

            findCurrencyByCodeStub.resolves(existingCurrency);

            const response = await app.inject({
                method: "POST",
                url: "/v1/currencies?token=valid-token",
                payload: currencyData
            });

            expect(response.statusCode).to.equal(400);
            expect(JSON.parse(response.payload)).to.deep.include({
                message: "Validation error: Currency with code USD already exists"
            });
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("USD")).to.be.true;
            expect(createCurrencyStub.called).to.be.false;
        });

        it("should validate required fields", async () => {
            const invalidCurrencyData = {
                code: "TEST",
                name: "Test Currency"
            };

            const response = await app.inject({
                method: "POST",
                url: "/v1/currencies?token=valid-token",
                payload: invalidCurrencyData
            });

            expect(response.statusCode).to.equal(400);
            const responseBody = JSON.parse(response.payload);
            expect(responseBody.code).to.equal(3);
            expect(responseBody.message).to.include("Validation error");
            expect(createCurrencyStub.called).to.be.false;
        });

        it("should validate iso object structure", async () => {
            const invalidCurrencyData = {
                code: "TEST",
                name: "Test Currency",
                iso: {
                    code: "TEST"
                }
            };

            const response = await app.inject({
                method: "POST",
                url: "/v1/currencies?token=valid-token",
                payload: invalidCurrencyData
            });

            expect(response.statusCode).to.equal(400);
            const responseBody = JSON.parse(response.payload);
            expect(responseBody.code).to.equal(3);
            expect(responseBody.message).to.include("Validation error");
            expect(createCurrencyStub.called).to.be.false;
        });

        it("should create currency with all optional fields", async () => {
            const currencyData = {
                code: "PREMIUM",
                name: "Premium Currency",
                iso: {
                    code: "PREM",
                    number: "997",
                    minorUnits: 2
                },
                isVirtual: false,
                isSocial: true,
                disableGGR: false,
                toEURMultiplier: 1.2,
                copyLimitsFrom: "EUR",
                provider: "custom",
                clientMinorUnits: 2,
                clientMoneyFormat: {
                    code: "P",
                    shortMode: true
                },
                originCurrency: {
                    currency: "EUR",
                    multiplier: 1.2
                }
            };

            findCurrencyByCodeStub.resolves(null);
            createCurrencyStub.resolves(currencyData);

            const response = await app.inject({
                method: "POST",
                url: "/v1/currencies?token=valid-token",
                payload: currencyData
            });

            expect(response.statusCode).to.equal(201);
            expect(JSON.parse(response.payload)).to.deep.equal(currencyData);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("PREMIUM")).to.be.true;
            expect(createCurrencyStub.calledOnceWith(currencyData)).to.be.true;
        });
    });

    describe("PATCH /v1/currencies/:code", () => {
        it("should update a currency name", async () => {
            const code = "USD";
            const updateData = {
                name: "Updated US Dollar"
            };

            const updatedCurrency = {
                code: "USD",
                name: "Updated US Dollar",
                iso: {
                    code: "USD",
                    number: "840",
                    minorUnits: 2
                },
                isVirtual: false,
                isSocial: false,
                disableGGR: false
            };

            updateCurrencyStub.resolves(updatedCurrency);

            const response = await app.inject({
                method: "PATCH",
                url: `/v1/currencies/${code}?token=valid-token`,
                payload: updateData
            });

            expect(response.statusCode).to.equal(200);
            expect(JSON.parse(response.payload)).to.deep.equal(updatedCurrency);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(updateCurrencyStub.calledOnceWith(code, updateData)).to.be.true;
        });

        it("should update currency properties", async () => {
            const code = "BTC";
            const updateData = {
                disableGGR: true,
                originCurrency: {
                    currency: "EUR",
                    multiplier: 0.85
                }
            };

            const updatedCurrency = {
                code: "BTC",
                name: "Bitcoin",
                iso: {
                    code: "BTC",
                    number: "999",
                    minorUnits: 8
                },
                isVirtual: true,
                isSocial: false,
                disableGGR: true,
                originCurrency: {
                    currency: "EUR",
                    multiplier: 0.85
                }
            };

            updateCurrencyStub.resolves(updatedCurrency);

            const response = await app.inject({
                method: "PATCH",
                url: `/v1/currencies/${code}?token=valid-token`,
                payload: updateData
            });

            expect(response.statusCode).to.equal(200);
            expect(JSON.parse(response.payload)).to.deep.equal(updatedCurrency);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(updateCurrencyStub.calledOnceWith(code, updateData)).to.be.true;
        });

        it("should update currency with new schema fields", async () => {
            const code = "PREMIUM";
            const updateData = {
                toEURMultiplier: 1.5,
                copyLimitsFrom: "USD",
                provider: "updated-provider",
                clientMinorUnits: 4,
                clientMoneyFormat: {
                    code: "PREM",
                    shortMode: false
                }
            };

            const updatedCurrency = {
                code: "PREMIUM",
                name: "Premium Currency",
                iso: {
                    code: "PREM",
                    number: "997",
                    minorUnits: 2
                },
                isVirtual: false,
                isSocial: true,
                disableGGR: false,
                toEURMultiplier: 1.5,
                copyLimitsFrom: "USD",
                provider: "updated-provider",
                clientMinorUnits: 4,
                clientMoneyFormat: {
                    code: "PREM",
                    shortMode: false
                }
            };

            updateCurrencyStub.resolves(updatedCurrency);

            const response = await app.inject({
                method: "PATCH",
                url: `/v1/currencies/${code}?token=valid-token`,
                payload: updateData
            });

            expect(response.statusCode).to.equal(200);
            expect(JSON.parse(response.payload)).to.deep.equal(updatedCurrency);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(updateCurrencyStub.calledOnceWith(code, updateData)).to.be.true;
        });

        it("should return 404 when currency not found", async () => {
            const code = "XYZ";
            const updateData = {
                name: "Non-existent Currency"
            };

            updateCurrencyStub.resolves(null);

            const response = await app.inject({
                method: "PATCH",
                url: `/v1/currencies/${code}?token=valid-token`,
                payload: updateData
            });

            expect(response.statusCode).to.equal(404);
            expect(JSON.parse(response.payload)).to.deep.equal({ error: `Currency with code ${code} not found` });
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(updateCurrencyStub.calledOnceWith(code, updateData)).to.be.true;
        });
    });

    describe("DELETE /v1/currencies/:code", () => {
        it("should delete a currency", async () => {
            const code = "USD";
            deleteCurrencyStub.resolves(true);

            const response = await app.inject({
                method: "DELETE",
                url: `/v1/currencies/${code}?token=valid-token`
            });

            expect(response.statusCode).to.equal(204);
            expect(response.payload).to.equal("");
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(deleteCurrencyStub.calledOnceWith(code)).to.be.true;
        });

        it("should return 404 when currency not found", async () => {
            const code = "XYZ";
            deleteCurrencyStub.resolves(false);

            const response = await app.inject({
                method: "DELETE",
                url: `/v1/currencies/${code}?token=valid-token`
            });

            expect(response.statusCode).to.equal(404);
            expect(JSON.parse(response.payload)).to.deep.equal({ error: `Currency with code ${code} not found` });
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(deleteCurrencyStub.calledOnceWith(code)).to.be.true;
        });
    });
});
