import { expect } from "chai";
import { Currencies } from "../skywind/currencies";
import artificialConfigs from "../skywind/resources/artificial_currencies.json";
import currencyConfigs from "../skywind/resources/currencies.json";

describe("Currency", () => {

    it("to minor units", () => {
        const value = Currencies.get("USD").toMinorUnits(499.78);
        expect(value).equal(49978);
    });

    it("to major units", () => {
        const value = Currencies.get("USD").toMajorUnits(49978);
        expect(value).equal(499.78);
    });

    it("format", () => {
        expect(Currencies.get("USD").format(499.7856789)).equal(499.78);
        expect(Currencies.get("USD").format(499)).equal(499);
        expect(Currencies.get("USD").format(-499.7856789)).equal(-499.78);
        expect(Currencies.get("USD").format(1.9999999999999)).equal(1.99);
    });

    it("format low values", () => {
        expect(Currencies.get("USD").format(0.0002)).equal(0);
        expect(Currencies.get("USD").format(0.0000002)).equal(0);
        expect(Currencies.get("USD").format(1e-20)).equal(0);
    });

    it("to fixed by exponent", () => {
        expect(Currencies.get("ILS").toFixedByExponent(199999903822.97998)).to.be.equal(199999903822.98);
        expect(Currencies.get("BHD").toFixedByExponent(199999822.97999998)).to.be.equal(199999822.980);
        expect(Currencies.get("BIF").toFixedByExponent(199993822)).to.be.equal(199993822);
    });

    it("supports virtual currencies", () => {
        const currency = Currencies.get("BNS");
        expect(currency.multiplier).equal(100);
        expect(currency.exponent).equal(2);
        expect(currency.toMinorUnits(1000)).equal(100000);
        expect(currency.toMajorUnits(1000)).equal(10);
    });

    it("supports WSC currency", () => {
        const currency = Currencies.get("WSC");
        expect(currency.multiplier).equal(100);
        expect(currency.exponent).equal(2);
        expect(currency.toMinorUnits(1000)).equal(100000);
        expect(currency.toMajorUnits(1000)).equal(10);
    });

    it("supports clientMinorUnits", () => {
        for (const code of [ "DOG", "BTC", "ETH", "LTC" ]) {
            const currency = Currencies.get(code);
            expect(currency.multiplier).equal(1);
            expect(currency.iso.minorUnits).equal(0);
            expect(currency.exponent).equal(0);
            expect(currency.clientMultiplier).equal(100);
            expect(currency.toMinorUnits(1000)).equal(1000);
            expect(currency.toMajorUnits(1000)).equal(1000);
            expect(currency.toFixedByExponent(1000)).equal(1000);
            expect(currency.format(1000)).equal(1000);
        }

        const usd = Currencies.get("USD");
        expect(usd.multiplier).equal(100);
        expect(usd.iso.minorUnits).equal(2);
        expect(usd.clientMultiplier).equal(100);
    });

    it("supports currencyMultiplier", () => {
        const currency = Currencies.get("BTC");
        const result = currency.clientMultiplier * 80;
        const epsilon = Math.round(result) - result;
        expect(Math.abs(epsilon) < 0.00001).true;
    });

    it("artificial currencies", () => {
        for (const artificial of Object.values(artificialConfigs)) {
            const currency = Currencies.value(artificial.currency);
            expect(currency.code).equal(artificial.currency);
            expect(currency.originCurrency?.currency).equal(artificial.originCurrency);
            expect(currency.originCurrency?.multiplier).equal(artificial.multiplier);
        }
    });

    it("disable GGR", () => {
        for (const [ code, currencyConfig ] of Object.entries(currencyConfigs)) {
            const currency = Currencies.value(code);
            if ((currencyConfig as unknown as { disableGGR?: boolean }).disableGGR) {
                expect(currency.disableGGR).equal(true);
            } else {
                expect(currency.disableGGR ?? false).equal(false);
            }
        }
    });
});
