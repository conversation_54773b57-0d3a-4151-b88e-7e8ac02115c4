import { expect } from "chai";
import * as sinon from "sinon";
import { getCurrencyModel } from "../../skywind/models/currency";
import type { DeepPartial } from "../../skywind/services/currencies";
import {
    findCurrencyByCode,
    getAllCurrencies,
    createCurrency,
    updateCurrency,
    deleteCurrency
} from "../../skywind/services/currencies";
import type { CurrencyType } from "../../skywind/models/currency";
import { Currency } from "@skywind-group/sw-currency-exchange";
import { ValidationError } from "../../skywind/errors";

describe("Currency Model", () => {
    const currencyModel = getCurrencyModel();
    let findByPkStub: sinon.SinonStub;
    let findAllStub: sinon.SinonStub;
    let createStub: sinon.SinonStub;
    let updateStub: sinon.SinonStub;
    let destroyStub: sinon.SinonStub;

    beforeEach(() => {
        findByPkStub = sinon.stub(currencyModel, "findByPk");
        findAllStub = sinon.stub(currencyModel, "findAll");
        createStub = sinon.stub(currencyModel, "create");
        updateStub = sinon.stub(currencyModel, "update");
        destroyStub = sinon.stub(currencyModel, "destroy");
    });

    afterEach(() => {
        sinon.restore();
    });

    describe("findCurrencyByCode", () => {
        it("should return a currency when found", async () => {
            const mockCurrencyRecord = {
                code: "USD",
                name: "US Dollar",
                properties: {
                    iso: {
                        code: "USD",
                        number: "840",
                        minorUnits: 2
                    },
                    toEURMultiplier: 1.1,
                    copyLimitsFrom: undefined,
                    provider: undefined,
                    clientMinorUnits: undefined,
                    clientMoneyFormat: undefined
                },
                type: null,
                disableGGR: false,
                originCurrencyCode: null,
                originCurrencyMultiplier: null
            };

            findByPkStub.resolves(mockCurrencyRecord);

            const result = await findCurrencyByCode("USD");
            expect(result).to.be.instanceOf(Currency);
            expect(result.code).to.equal("USD");
            expect(result.name).to.equal("US Dollar");
            expect(result.iso.code).to.equal("USD");
            expect(result.iso.number).to.equal("840");
            expect(result.iso.minorUnits).to.equal(2);
            expect(findByPkStub.calledOnceWith("USD")).to.be.true;
        });

        it("should return null when currency not found", async () => {
            findByPkStub.resolves(null);

            const result = await findCurrencyByCode("XYZ");
            expect(result).to.be.null;
            expect(findByPkStub.calledOnceWith("XYZ")).to.be.true;
        });
    });

    describe("getAllCurrencies", () => {
        it("should return all currencies", async () => {
            const mockCurrencyRecords = [
                {
                    code: "USD",
                    name: "US Dollar",
                    properties: {
                        iso: {
                            code: "USD",
                            number: "840",
                            minorUnits: 2
                        },
                        toEURMultiplier: 1.1,
                        copyLimitsFrom: undefined,
                        provider: undefined,
                        clientMinorUnits: undefined,
                        clientMoneyFormat: undefined
                    },
                    type: null,
                    disableGGR: false,
                    originCurrencyCode: null,
                    originCurrencyMultiplier: null
                },
                {
                    code: "EUR",
                    name: "Euro",
                    properties: {
                        iso: {
                            code: "EUR",
                            number: "978",
                            minorUnits: 2
                        },
                        toEURMultiplier: 1.0,
                        copyLimitsFrom: undefined,
                        provider: undefined,
                        clientMinorUnits: undefined,
                        clientMoneyFormat: undefined
                    },
                    type: null,
                    disableGGR: false,
                    originCurrencyCode: null,
                    originCurrencyMultiplier: null
                }
            ];

            findAllStub.resolves(mockCurrencyRecords);

            const result = await getAllCurrencies();
            expect(result).to.be.an("array").with.lengthOf(2);
            expect(result[0]).to.be.instanceOf(Currency);
            expect(result[0].code).to.equal("USD");
            expect(result[0].name).to.equal("US Dollar");
            expect(result[1]).to.be.instanceOf(Currency);
            expect(result[1].code).to.equal("EUR");
            expect(result[1].name).to.equal("Euro");
            expect(findAllStub.calledOnce).to.be.true;
        });
    });

    describe("createCurrency", () => {
        it("should create a currency", async () => {
            const currencyData: CurrencyType = {
                code: "BTC",
                name: "Bitcoin",
                iso: {
                    code: "BTC",
                    number: "999",
                    minorUnits: 8
                },
                isVirtual: true,
                isSocial: false
            };

            const mockCreatedCurrencyRecord = {
                code: "BTC",
                name: "Bitcoin",
                properties: {
                    iso: {
                        code: "BTC",
                        number: "999",
                        minorUnits: 8
                    },
                    toEURMultiplier: undefined,
                    copyLimitsFrom: undefined,
                    provider: undefined,
                    clientMinorUnits: undefined,
                    clientMoneyFormat: undefined
                },
                type: "virtual",
                disableGGR: false,
                originCurrencyCode: null,
                originCurrencyMultiplier: null
            };

            createStub.resolves(mockCreatedCurrencyRecord);

            const result = await createCurrency(currencyData);
            expect(result).to.be.instanceOf(Currency);
            expect(result.code).to.equal("BTC");
            expect(result.name).to.equal("Bitcoin");
            expect(result.isVirtual).to.be.true;
            expect(createStub.calledOnce).to.be.true;
        });

        it("should create a virtual currency with origin currency", async () => {
            const currencyData: CurrencyType = {
                code: "VTC",
                name: "Virtual Test Coin",
                iso: {
                    code: "VTC",
                    number: "998",
                    minorUnits: 2
                },
                isVirtual: true,
                isSocial: false,
                originCurrency: {
                    currency: "USD",
                    multiplier: 100
                }
            };

            const mockCreatedCurrencyRecord = {
                code: "VTC",
                name: "Virtual Test Coin",
                properties: {
                    iso: {
                        code: "VTC",
                        number: "998",
                        minorUnits: 2
                    },
                    toEURMultiplier: undefined,
                    copyLimitsFrom: undefined,
                    provider: undefined,
                    clientMinorUnits: undefined,
                    clientMoneyFormat: undefined
                },
                type: "virtual",
                disableGGR: false,
                originCurrencyCode: "USD",
                originCurrencyMultiplier: 100
            };

            createStub.resolves(mockCreatedCurrencyRecord);

            const result = await createCurrency(currencyData);
            expect(result).to.be.instanceOf(Currency);
            expect(result.code).to.equal("VTC");
            expect(result.name).to.equal("Virtual Test Coin");
            expect(result.isVirtual).to.be.true;
            expect(result.originCurrency).to.deep.equal({
                currency: "USD",
                multiplier: 100
            });
            expect(createStub.calledOnce).to.be.true;
        });
    });

    describe("updateCurrency", () => {
        it("should update a currency and return the updated currency", async () => {
            const code = "USD";
            const updateData: DeepPartial<CurrencyType> = {
                name: "Updated US Dollar"
            };

            const mockExistingCurrencyRecord = {
                code: "USD",
                name: "US Dollar",
                properties: {
                    iso: {
                        code: "USD",
                        number: "840",
                        minorUnits: 2
                    },
                    toEURMultiplier: 1.1
                },
                type: null,
                disableGGR: false,
                originCurrencyCode: null,
                originCurrencyMultiplier: null
            };

            const mockUpdatedCurrencyRecord = {
                code: "USD",
                name: "Updated US Dollar",
                properties: {
                    iso: {
                        code: "USD",
                        number: "840",
                        minorUnits: 2
                    },
                    toEURMultiplier: 1.1
                },
                type: null,
                disableGGR: false,
                originCurrencyCode: null,
                originCurrencyMultiplier: null
            };

            findByPkStub.onFirstCall().resolves(mockExistingCurrencyRecord);
            findByPkStub.onSecondCall().resolves(mockUpdatedCurrencyRecord);
            updateStub.resolves([1]);

            const result = await updateCurrency(code, updateData);
            expect(result).to.be.instanceOf(Currency);
            expect(result.name).to.equal("Updated US Dollar");
        });

        it("should throw ValidationError when currency not found", async () => {
            const code = "XYZ";
            const updateData: DeepPartial<CurrencyType> = {
                name: "Non-existent Currency"
            };

            findByPkStub.resolves(null);

            try {
                await updateCurrency(code, updateData);
                expect.fail("Expected ValidationError to be thrown");
            } catch (error) {
                expect(error).to.be.instanceOf(ValidationError);
                expect(error.message).to.equal("Validation error: Currency not found");
            }
        });
    });

    describe("deleteCurrency", () => {
        it("should delete a currency and return true", async () => {
            const code = "USD";
            destroyStub.resolves(1);

            const result = await deleteCurrency(code);
            expect(result).to.be.true;
            expect(destroyStub.calledOnceWith({ where: { code } })).to.be.true;
        });

        it("should return false when currency not found", async () => {
            const code = "XYZ";
            destroyStub.resolves(0);

            const result = await deleteCurrency(code);
            expect(result).to.be.false;
            expect(destroyStub.calledOnceWith({ where: { code } })).to.be.true;
        });
    });
});
