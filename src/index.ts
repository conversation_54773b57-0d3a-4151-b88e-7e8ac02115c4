import { OXRCurrencyProvider } from "./skywind/providers/oxrCurrencyProvider";
import { OANDACurrencyProvider } from "./skywind/providers/oandaCurrencyProvider";
import { SWSCurrencyProvider } from "./skywind/providers/swsCurrencyProvider";
import { DefaultCurrencyProvider } from "./skywind/providers/defaultCurrencyProvider";
import { CurrencyExchangeService } from "./skywind/currencyExchangeService";
import { type CurrencyExchange, type CurrencyProvider, ExchangeRateProvider, ExchangeRateType } from "./skywind/types";
import type { RedisPool } from "./skywind/redisTypes";
import config from "./skywind/config";

export * from "./skywind/types";
export * from "./skywind/redisTypes";
export * from "./skywind/currencies";
export * from "./skywind/externalCurrencyReplacement";
export * from "./skywind/gameLimitsCurrencies";

export async function createCurrencyExchange(pool: RedisPool, type = ExchangeRateType.BID): Promise<CurrencyExchange> {
    let provider: CurrencyProvider;
    switch (config.provider) {
        case ExchangeRateProvider.OXR:
            provider = new OXRCurrencyProvider(config.oxr.appKey);
            break;
        case ExchangeRateProvider.OANDA:
            provider = new OANDACurrencyProvider(config.oanda.apiKey);
            break;
        case ExchangeRateProvider.SWS:
            provider = new SWSCurrencyProvider(config.sws.baseUrl, config.sws.internalServerToken);
            break;
        default:
            provider = new DefaultCurrencyProvider();
    }
    const service = new CurrencyExchangeService();
    await service.init(pool, provider, config, type);
    return service;
}
