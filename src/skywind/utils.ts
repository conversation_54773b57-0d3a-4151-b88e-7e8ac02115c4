import * as jwt from "jsonwebtoken";
import { logging } from "@skywind-group/sw-utils";
import config from "./config";

export function getTimestamp(date: Date): string {
    return date.toISOString().substr(0, 10);
}

export function getPrevDay(date = new Date(), days = 1): Date {
    const prevDay = new Date(date);
    prevDay.setUTCDate(prevDay.getUTCDate() - days);
    prevDay.setUTCHours(0, 0, 0, 0);
    return prevDay;
}

export function getNextDay(date = new Date(), days = 1): Date {
    const nextDay = new Date(date);
    nextDay.setUTCDate(nextDay.getUTCDate() + days);
    nextDay.setUTCHours(0, 0, 0, 0);
    return nextDay;
}

export function getStartTime(date: Date): number {
    const dayStart = new Date(date);
    dayStart.setUTCHours(0, 0, 0, 0);
    return dayStart.getTime();
}

export function getEndTime(date: Date): number {
    const dayEnd = new Date(date);
    dayEnd.setUTCDate(date.getUTCDate() + 1);
    dayEnd.setUTCHours(0, 0, 0, 0);
    return dayEnd.getTime() - 1;
}

export function generateToken(tokenConfig): Promise<string> {
    const cfg = tokenConfig;
    return new Promise<string>((resolve, reject) => {
        jwt.sign({}, cfg.secret, {
            algorithm: cfg.algorithm,
            expiresIn: cfg.expiresIn,
            issuer: cfg.issuer,
        }, (err, token) => {
            return err ? reject(err) : resolve(token);
        });
    });
}

export async function got<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const token = await generateToken(config.sws.internalServerToken);
    const url = `${config.sws.baseUrl}/v1${endpoint}?token=${encodeURIComponent(token)}`;
    const response = await fetch(url, options);
    if (!response.ok) {
        if (options.method && ["POST", "PATCH"].includes(options.method)) {
            const errorBody = await response.json().catch(() => ({})) as any;
            throw new Error(`${errorBody?.message || "Request failed"}, status=${response.status}`);
        }
        throw new Error(`HTTP error, status: ${response.status}`);
    }
    if (options.method === "DELETE") {
        return undefined as T;
    }
    return await response.json() as T;
}

/* Hand-made cache that implemented to prevent multiple parallel queries */
export abstract class ConcurrentStorage<T> {
    protected data: T;
    private updating: Promise<T>;
    private updated: boolean;

    protected constructor(protected readonly log: logging.Logger) {
        // empty
    }

    protected abstract retrieve(): Promise<T>;

    protected async fetch(): Promise<T> {
        if (this.updated) {
            this.updated = false;
            this.updating = undefined;
        }
        if (!this.data) {
            if (!this.updating) {
                this.updating = this.update();
            }
            return this.updating;
        } else {
            return this.data;
        }
    }

    public reset(): void {
        this.data = undefined;
    }

    private async update(): Promise<T> {
        try {
            this.data = await this.retrieve();
        } catch (e) {
            this.log.error(e);
        } finally {
            this.updated = true;
        }
        return this.data;
    }
}
