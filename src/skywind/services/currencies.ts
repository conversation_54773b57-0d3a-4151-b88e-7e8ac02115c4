import type { Currency } from "@skywind-group/sw-currency-exchange";
import { ValidationError } from "../errors";
import { merge } from "lodash";
import { getCurrencyModel, toCurrency, toCurrencyAttributes, type CurrencyType } from "../models/currency";
import { sequelize as db } from "../models/db";
import type * as Sequelize from "sequelize";

/**
 * Makes all properties in T optional and recursively applies the same to all nested objects
 */
export type DeepPartial<T> = T extends object ? {
    [P in keyof T]?: DeepPartial<T[P]>;
} : T;

export async function findCurrencyByCode(code: string): Promise<Currency | null> {
    const currencyModel = getCurrencyModel();
    const item = await currencyModel.findByPk(code);
    return item ? toCurrency(item) : null;
}

export async function getAllCurrencies(): Promise<Currency[]> {
    const currencyModel = getCurrencyModel();
    const items = await currencyModel.findAll();
    return items.map(toCurrency);
}

export async function createCurrency(data: Sequelize.Optional<CurrencyType, "code">) {
    return db.transaction(async (transaction) => {
        const currencyModel = getCurrencyModel();
        if (data.code) {
            const item = await currencyModel.findByPk(data.code, { transaction });
            if (item) {
                throw new ValidationError(`Currency with code ${data.code} already exists`);
            }
        }
        const currency = await currencyModel.create(toCurrencyAttributes(data), { transaction });
        return toCurrency(currency);
    });
}

export async function updateCurrency(code: string, data: DeepPartial<Omit<CurrencyType, "code">>) {
    return db.transaction(async (transaction) => {
        const currencyModel = getCurrencyModel();
        const item = await currencyModel.findByPk(code, { transaction });
        if (!item) {
            throw new ValidationError("Currency not found");
        }
        const [updatedCount] = await currencyModel.update(toCurrencyAttributes(merge(data, toCurrency(item))), {
            where: { code },
            transaction
        });
        if (updatedCount === 0) {
            return null;
        }
        return findCurrencyByCode(code);
    });
}

export async function deleteCurrency(code: string): Promise<boolean> {
    const currencyModel = getCurrencyModel();
    const deleted = await currencyModel.destroy({
        where: { code }
    });
    return deleted > 0;
}
