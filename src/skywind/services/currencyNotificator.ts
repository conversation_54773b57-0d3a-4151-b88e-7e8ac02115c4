import type { Currency } from "@skywind-group/sw-currency-exchange";
import type { Messaging } from "@skywind-group/sw-messaging";
import { createMessaging } from "@skywind-group/sw-messaging";
import type { logging } from "@skywind-group/sw-utils";
import { lazy } from "@skywind-group/sw-utils";
import config from "../config";

export interface CurrencyNotificator {
    notifyCreate(currency: Currency): Promise<void>;
    notifyUpdate(currency: Currency): Promise<void>;
    notifyDelete(code: string): Promise<void>;
}

export function getCurrencyNotificatorFactory() {
    return lazy<CurrencyNotificator>(getCurrencyNotificator);
}

function getCurrencyNotificator(log: logging.Logger) {
    if (config.currencyNotification.type === "nats" && config.nats) {
        const messaging = createMessaging(config.nats, log);
        return new NatsCurrencyNotificator(messaging, config.currencyNotification.channelPrefix);
    }
}

class NatsCurrencyNotificator implements CurrencyNotificator {
    constructor(private readonly messaging: Messaging, private readonly channelPrefix: string) {
    }

    public notifyCreate(currency: Currency) {
        return this.messaging.publish(`${this.channelPrefix}.create`, currency);
    }

    public notifyUpdate(currency: Currency) {
        return this.messaging.publish(`${this.channelPrefix}.update`, currency);
    }

    public notifyDelete(code: string) {
        return this.messaging.publish(`${this.channelPrefix}.delete`, code);
    }
}
