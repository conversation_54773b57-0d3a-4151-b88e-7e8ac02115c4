import type { logging } from "@skywind-group/sw-utils";

const config = {

    appPort: +process.env.APP_PORT || 3030,
    internalPort: +process.env.APP_INTERNAL_PORT || 4004,

    bodyParserJsonLimit: +process.env.BODY_PARSER_JSON_LIMIT || 5242880,
    compressionThreshold: +process.env.COMPRESSION_THRESHOLD || 1024,

    // logging all POSTGRES queries to console
    queryLogging: process.env.POSTGRES_QUERY_LOGGING === "true",

    db: {
        database: process.env.PGDATABASE || "management",
        user: process.env.PGUSER,
        password: process.env.PGPASSWORD,
        host: process.env.PGHOST || "db",
        port: +process.env.PGPORT || 5432,
        ssl: {
            isEnabled: process.env.PG_SECURE_CONNECTION === "true",
            ca: process.env.PG_CA_CERT || "./ca.pem",
        },
        maxConnections: +process.env.PG_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.PG_MAX_IDLE_TIME_MS || 30000,
        schema: process.env.PGSCHEMA || "public",
        syncOnStart: process.env.SYNC_ON_START === "true"
    },

    oxr: {
        // secret key for access to OXR
        appKey: process.env.OXR_APP_KEY, // "d896208c58f142bdbabb7469323e17c8", // Anastasia Kostyukova's test app key
    },

    oanda: {
        // api key to access OANDA
        url: process.env.OANDA_URL || "https://web-services.oanda.com/rates/api/v2/rates/candle.json",
        apiKey: process.env.OANDA_API_KEY, // "************************", // BI API key
    },

    // List of base currencies to fetch crosses for
    baseCurrencies: (process.env.CURRENCY_RATES_BASE_CURRENCIES || "EUR,USD,CNY,KRW,MYR").split(","),

    // Currency rates provider: "default", "oxr" or "oanda"
    provider: process.env.CURRENCY_RATES_PROVIDER || "default",

    job: {
        // Cron schedule when fetch new currency rates from provider
        updateSchedule: process.env.CURRENCY_RATES_UPDATE_SCHEDULE || "0 1 * * *",
        // For how long retry to fetch currency rates from provider before falling back to previous date rates
        updateFallbackTimeout: +process.env.CURRENCY_RATES_UPDATE_EXPIRE_MSEC || (22 * 60 * 60 * 1000),
        // timeout to retry to get data
        updateRetryTimeout: +process.env.CURRENCY_RATES_UPDATE_TIMEOUT_MSEC || (10 * 60 * 1000),
        // timeout to retry fallback
        fallbackRetryTimeout: 60000
    },

    internalServerToken: {
        expiresIn: +process.env.INTERNAL_SERVER_TOKEN_EXPIRES_IN || 300,
        algorithm: process.env.INTERNAL_SERVER_TOKEN_ALGORITHM || "HS256",
        issuer: process.env.INTERNAL_SERVER_TOKEN_ISSUER || "skywindgroup",
        secret: process.env.INTERNAL_SERVER_TOKEN_SECRET || "TU8N9oP4pPfrUMaRYkjwBsOyw0hgg39sPsTjONrgnN1ErJbn2"
    },

    maxDateRange: +process.env.CURRENCY_RATES_MAX_DATE_RANGE || 31,

    currencyNotification: {
        channelPrefix: process.env.CURRENCY_NOTIFICATION_CHANNEL || "currency-notification",
        type: process.env.CURRENCY_NOTIFICATION_TYPE
    },

    nats: {
        servers: JSON.parse(process.env.NATS_SERVERS || `["${process.env.NATS_URL || "nats://nats:4222"}"]`)
    },
};

type LogConfig = logging.OutputConfig & {
    host?: string;
    port?: number;
    filterFacility?: string;
    additionalTopic?: string;
    isReplaceMainTopicEnabled?: boolean;
};
export function logConfig(): LogConfig[] {
    const logLevel = process.env.LOG_LEVEL || "info";
    const loggingOutput = process.env.LOGGING_OUTPUT_TYPE || "console";
    if (loggingOutput === "kafka") {
        const config: LogConfig = {
            type: "kafka",
            logLevel
        };
        if (process.env.EVENT_KAFKA_ENABLED === "true") {
            config.filterFacility = process.env.EVENT_FILTER_FACILITY || "sw-events";
            config.additionalTopic = process.env.KAFKA_EVENT_TOPIC || "sw-logging";
            config.isReplaceMainTopicEnabled = process.env.KAFKA_REPLACE_MAIN_TOPIC_ENABLED === "true";
        }
        return [config];
    }
    const graylog = {
        host: process.env.GRAYLOG_HOST || undefined,
        port: +process.env.GRAYLOG_PORT || undefined,
    };
    if (graylog.host && graylog.port) {
        const configs: LogConfig[] = [{
            type: "graylog",
            logLevel,
            ...graylog
        }];
        const graylogEvent = {
            host: process.env.GRAYLOG_EVENT_HOST || undefined,
            port: +process.env.GRAYLOG_EVENT_PORT || undefined,
        };
        if (graylogEvent.host && graylogEvent.port) {
            configs.push({
                type: "graylog",
                logLevel: process.env.GRAYLOG_EVENT_LOG_LEVEL || "debug",
                ...graylogEvent,
                filterFacility: process.env.EVENT_FILTER_FACILITY || "sw-events"
            });
        }
        return configs;
    }
    return [{
        type: "console",
        logLevel
    }];
}

export default config;
