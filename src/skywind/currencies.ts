import { logging } from "@skywind-group/sw-utils";
import { CurrenciesStorage } from "./currenciesStorage";
import type { Currency } from "./currency";
import { DeepPartial } from "./types";
import { createCurrency, CurrencyCreateType, CurrencyType, deleteCurrency, updateCurrency } from "./currenciesService";

const storage = new CurrenciesStorage(logging.logger("currency-storage-service"));

export const Currencies = {

    async init() {
        await storage.init();
    },

    exists(currencyCode: string): boolean {
        try {
            this.get(currencyCode);
            return true;
        } catch (err) {
            return false;
        }
    },

    keys() {
        return storage.keys();
    },

    values() {
        return storage.values();
    },

    artificialValues(): Readonly<Currency[]> {
        return Object.freeze(this.values().filter(({ originCurrency }) => Boolean(originCurrency)));
    },

    value(currencyCode: string) {
        try {
            return this.get(currencyCode);
        } catch (err) {
            return undefined;
        }
    },

    get(code: string) {
        return storage.get(code);
    },

    verifyExists(currencyCode: string) {
        this.get(currencyCode);
    },

    format(currencyCode: string, value: number): number {
        return this.get(currencyCode).format(value);
    },

    create(data: CurrencyCreateType) {
        return createCurrency(data);
    },

    update(code: string, data: DeepPartial<CurrencyType>) {
        return updateCurrency(code, data);
    },

    delete(code: string) {
        return deleteCurrency(code);
    }
};
