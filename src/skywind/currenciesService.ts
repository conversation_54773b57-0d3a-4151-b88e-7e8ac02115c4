import { Currency } from "./currency";
import type { DeepPartial, Optional } from "./types";
import { got } from "./utils";

export type CurrencyType = Pick<Currency, "code" | "name" | "iso" | "isVirtual" | "isSocial" | "disableGGR" | "originCurrency">;

function asCurrency(item: CurrencyType): Currency {
    return new Currency(
        item.code,
        item.name,
        item.iso,
        item.isVirtual ? "virtual" : item.isSocial ? "social" : undefined,
        undefined,
        undefined,
        undefined,
        item.originCurrency,
        undefined,
        undefined,
        item.disableGGR
    );
}

export async function getCurrencies(): Promise<Currency[]> {
    const items = await got<CurrencyType[]>("/currencies");
    return items.map(asCurrency);
}

export async function getCurrency(code: string): Promise<Currency> {
    const item = await got<CurrencyType>(`/currencies/${code}`);
    return asCurrency(item);
}

export type CurrencyUpdateType = DeepPartial<CurrencyType>;

export async function updateCurrency(code: string, data: CurrencyUpdateType): Promise<Currency> {
    const item = await got<CurrencyType>(`/currencies/${code}`, {
        method: "PATCH",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
    });
    return asCurrency(item);
}

export type CurrencyCreateType = Optional<CurrencyType, "isSocial" | "isVirtual">;

export async function createCurrency(data: CurrencyCreateType): Promise<Currency> {
    const item = await got<CurrencyType>("/currencies", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
    });
    return asCurrency(item);
}

export async function deleteCurrency(code: string): Promise<void> {
    return got<void>(`/currencies/${code}`, {
        method: "DELETE",
    });
}
