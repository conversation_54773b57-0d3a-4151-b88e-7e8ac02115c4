import { logging } from "@skywind-group/sw-utils";
import type { Currency } from "./currency";
import { getCurrencies } from "./currenciesService";
import { ConcurrentStorage } from "./utils";
import { CurrencyNotFoundError } from "./types";
import { records } from "./currenciesResource";

export class CurrenciesStorage extends ConcurrentStorage<Record<string, Currency>> {

    constructor(log: logging.Logger) {
        super(log);
    }

    async init() {
        await this.fetch();
    }

    get(code: string) {
        let currency = this.data[code];
        if (!currency) {
            this.fetch().catch(this.log.error);
            currency = records[code];
            if (!currency) {
                throw new CurrencyNotFoundError(code);
            }
        }
        return currency;
    }

    keys() {
        return Object.keys(this.data);
    }

    values() {
        return Object.values(this.data);
    }

    protected async retrieve() {
        const currencies = await getCurrencies();
        const result: Record<string, Currency> = {};
        for (const currency of Object.values(records)) {
            result[currency.code] = currency;
        }
        for (const currency of currencies) {
            result[currency.code] = currency;
        }
        return result;
    }
}
