import { Currency } from "./currency";
import currenciesConfig from "./resources/currencies.json";
import artificialConfig from "./resources/artificial_currencies.json";

interface ClientMoneyFormat {
    code?: string;
}

interface CurrencyInfo {
    readonly name: string;
    readonly iso: Currency["iso"];
    readonly type?: "virtual" | "social";
    readonly toEURMultiplier?: number;
    readonly copyLimitsFrom?: string;
    readonly provider?: string;
    /** Used to update currencyMultiplier on game-server for "Bank It" feature */
    readonly clientMinorUnits?: number;
    readonly clientMoneyFormat?: ClientMoneyFormat;
    readonly disableGGR?: boolean;
}

interface ArtificialInfo {
    currency: string;
    originCurrency: string;
    multiplier: number;
}

function toCurrency(code: string, info: CurrencyInfo) {
    const artificialCurrency: ArtificialInfo = artificialConfig[code];
    return new Currency(
        code,
        info.name,
        info.iso,
        info.type,
        info.toEURMultiplier,
        info.copyLimitsFrom,
        info.provider,
        artificialCurrency ? {
            currency: artificialCurrency.originCurrency,
            multiplier: artificialCurrency.multiplier
        } : undefined,
        info.clientMinorUnits,
        info.clientMoneyFormat,
        info.disableGGR
    );
}

export const records = Object.fromEntries(Object.entries(currenciesConfig as Record<string, CurrencyInfo>).map(([code, item]) => [
    code,
    toCurrency(code, item)
]));
