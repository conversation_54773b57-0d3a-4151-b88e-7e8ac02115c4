const EPSILON = 10 ** -12;

export interface OriginCurrency {
    currency: string;
    multiplier: number;
}

interface Iso {
    code: string;
    number: string;
    /** Number of decimal digits after comma (example: 3 == ".000") */
    minorUnits: number;
}

interface ClientMoneyFormat {
    code?: string;
    /** Enables short mode postfixes K:10^3, M:10^6, B:10^9 (example: $1500 = $1.5K ) */
    shortMode?: boolean;
}

export class Currency {
    /** Virtual currencies for promotion rewards. */
    public readonly isVirtual: boolean;
    public readonly isSocial: boolean;
    public readonly multiplier: number;
    public readonly exponent: number;
    public readonly clientMultiplier: number;

    constructor(
        public readonly code: string,
        public readonly name: string,
        public readonly iso: Iso,
        type?: "virtual" | "social",
        public readonly toEURMultiplier?: number,
        public readonly copyLimitsFrom?: string,
        public readonly provider?: string,
        public readonly originCurrency?: OriginCurrency,
        clientMinorUnits?: number,
        public readonly clientMoneyFormat?: ClientMoneyFormat,
        public readonly disableGGR?: boolean
    ) {
        this.isVirtual = type === "virtual";
        this.isSocial = type === "social";
        this.multiplier = 10 ** iso.minorUnits;
        this.exponent = String(this.multiplier).length - 1;
        this.clientMultiplier = clientMinorUnits !== undefined ? (10 ** clientMinorUnits) : this.multiplier;
    }

    public toMinorUnits(value: number): number {
        return Math.round(value * this.multiplier);
    }

    public toMajorUnits(value: number): number {
        if (!Number.isInteger(value)) {
            throw new Error("Invalid currency value: " + value);
        }
        return value / this.multiplier; // assuming the result mustn't have an accumulated floating point error
    }

    public format(value: number): number {
        const precision = 10 ** this.exponent;
        return Math.sign(value) * Math.floor(Math.abs(value) * precision + EPSILON) / precision;
    }

    public toFixedByExponent(value: number): number {
        return Number((value).toFixed(this.exponent));
    }
}
