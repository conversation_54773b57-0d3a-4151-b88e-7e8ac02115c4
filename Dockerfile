FROM node:22-alpine

# Install certificate
RUN apk add ca-certificates curl git && \
    curl -o /usr/local/share/ca-certificates/ISRG_Root_X2.crt https://letsencrypt.org/certs/isrg-root-x2.pem && \
    update-ca-certificates

RUN mkdir -p /opt/fcnce && \
    adduser -u 2005 -h /opt/fcnce -Ds /sbin/nologin fcnce

WORKDIR /app
COPY . /app/

RUN npm install && \
    npm run compile && \
    npm run version && \
    rm .npmrc

EXPOSE 3030

CMD ["node", "lib/skywind/app"]
